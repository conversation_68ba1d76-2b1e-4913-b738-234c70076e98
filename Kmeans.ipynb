from google.colab import drive
drive.mount("/content/drive")

import pandas as pd
import numpy as np
import seaborn as sns
import matplotlib.pyplot as plt

df = pd.read_csv("/content/drive/MyDrive/Bộ Môn (1)/Giảng Dạy/ML  2024 - 2025/KHTN Machine Learning co ban/Week06/marketing_campaign.csv")

df.head()

df.tail()

df.info()

df.describe()

df.hist(figsize=(15, 15))
plt.show()

cat_cols = [col for col in df.columns if df[col].dtypes=="O"] # với mỗi col trong df.columns sao cho df[col].dtypes là "O" (dạng Object) thì ta đưa col vào list

for col in cat_cols: #Với mỗi phần tử col có trong cat_cols
    sns.countplot(x=col, data=df) #ta vẽ hình dạng countplot cho từng col
    plt.show() #phải dùng nếu không thì chỉ vẽ hình cuối cùng trong list

data = df.copy()

data.loc[:, "Education"] = data["Education"].replace("2n Cycle", "Master")
data.loc[:, "Education"] = data["Education"].apply(lambda x: 0 if x=="Basic" else 1 if x=="Graduation" else 2 if x=="Master" else 3)
data = pd.get_dummies(data, columns=["Marital_Status"], dtype=int)
data.head()

df.Marital_Status.value_counts()

from sklearn.preprocessing import StandardScaler
scaler = StandardScaler()
scaled_data = scaler.fit_transform(data.values)

from yellowbrick.cluster import KElbowVisualizer
from sklearn.cluster import KMeans
elbow_method = KElbowVisualizer(KMeans(), k=(1,11))
elbow_method.fit(scaled_data)
elbow_method.show()

kmeans_model = KMeans(n_clusters=3)
kmeans_model.fit(scaled_data)

#để xuất ra label của tập training (scaled_data) ta dùng .labels_
kmeans_model.labels_

#để dự đoán ra label của tập testing (ta giả sử là scaled_data) ta dùng .predict
kmeans_model.predict(scaled_data)

#Để xuất ra các centroids ta có thể dùng .cluster_centers_
kmeans_model.cluster_centers_

from sklearn.decomposition import PCA #Khai báo thư viện PCA
pca = PCA(n_components=3) #khai báo
pca.fit(scaled_data)

#cho ta biết được các thành phần phủ được bao nhiêu lượng thông tin của data gốc
a = pca.explained_variance_ratio_ * 100

a

pca = PCA(n_components=3) #dùng pca 3 thành phần
pca_df = pd.DataFrame(pca.fit_transform(scaled_data), columns=["pca_1", "pca_2", "pca_3"])#tạo ra dataframe với 3 thành phàn là pca1, pca2, và pca3

pca_ = PCA(n_components=3)
pca_.fit(scaled_data)
pca_.explained_variance_ratio_

pca_df

plt.figure(figsize=(12, 7))
ax = plt.subplot(projection="3d")
ax.scatter(pca_df.pca_1, pca_df.pca_2, pca_df.pca_3, c=kmeans_model.labels_, cmap="cool")
plt.show()

pca = PCA(n_components=2)
pca.fit(scaled_data)
pca_df = pd.DataFrame(pca.transform(scaled_data), columns=["pca1", "pca2"])

pca.components_


pca_df.head()

plt.figure(figsize=(12, 7))
ax = plt.subplot()
ax.scatter(pca_df.pca1, pca_df.pca2, c=kmeans_model.labels_, cmap="cool")
plt.show()

plt.figure(figsize=(12, 7))
ax = plt.subplot()
group = kmeans_model.labels_
for g in np.unique(group):
    index = np.where(group==g)
    ax.scatter(pca_df.iloc[index].pca1, pca_df.iloc[index].pca2, label=g, cmap="cool")
ax.legend()
plt.show()

#Save model
import pickle #import thư viện pickle

file_name = "model.sav" #Tạo tên cũng như đường dẫn lưu model (nếu chỉ để tên model không thì sẽ lưu tại thư mục hiện hành)
pickle.dump(kmeans_model, open(file_name, "wb")) #nhận vào tên thư viện và đường dẫn lưu trữ model

file_name = "scaler.sav" #Tạo tên cũng như đường dẫn lưu model (nếu chỉ để tên model không thì sẽ lưu tại thư mục hiện hành)
pickle.dump(scaler, open(file_name, "wb")) #nhận vào tên thư viện và đường dẫn lưu trữ model

#Load model
file_name = "model.sav"
model = pickle.load(open(file_name, "rb"))

file_name = "scaler.sav"
scaler = pickle.load(open(file_name, "rb"))

model.labels_

cluster = model.labels_
cluster_0 = np.where(cluster==0) #index các data samples thuộc cluster 1
cluster_1 = np.where(cluster==1) #index các data samples thuộc cluster 2
cluster_2 = np.where(cluster==2) #index các data samples thuộc cluster 3

#Lấy ra các data samples thuộc cluster 1
data.iloc[cluster_0]

